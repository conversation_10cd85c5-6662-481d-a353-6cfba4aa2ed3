import { api } from "boot/axios";

const DOWNLOAD_URL = "/storage/download";
const LIST_URL = "/storage/list";
const UPLOAD_URL = "/storage/upload";
const DELETE_URL = "/storage/delete";

export const SCENARIO = "SCENARIO";
export const PLATFORM = "PLATFORM";
export const DEVICE = "DEVICE";
export const LINK = "LINK";
export const MODULATION = "MODULATION";
export const TRAFFIC = "TRAFFIC";
export const ITMPARAM = "ITMPARAM";

class ModelManager {
  constructor() {
    if (ModelManager.instance) {
      return ModelManager.instance;
    }

    this.modelSuffixMap = {
      SCENARIO: "scen.m",
      PLATFORM: "plt.m",
      DEVICE: "dev.m",
      LINK: "link.m",
      MODULATION: "md.m",
      TRAFFIC: "traf.m",
      ITMPARAM: "itmp.m",
    };
    this.modelCache = {
      SCENARIO: new Map(),
      PLATFORM: new Map(),
      DEVICE: new Map(),
      LINK: new Map(),
      MODULATION: new Map(),
      TRAFFIC: new Map(),
      ITMPARAM: new Map(),
    };

    ModelManager.instance = this;
  }

  static getInstance() {
    if (!ModelManager.instance) {
      ModelManager.instance = new ModelManager();
    }
    return ModelManager.instance;
  }

  async getModel(modelType, modelName) {
    if (this.modelCache[modelType].has(modelName)) {
      return JSON.parse(JSON.stringify(this.modelCache[modelType].get(modelName)));
    }

    return await this.loadModel(modelType, modelName);
  }

  async loadModel(modelType, modelName) {
    const modelUrl = `${DOWNLOAD_URL}?filename=/models/${modelType.toLowerCase()}s/${modelName}.${
      this.modelSuffixMap[modelType]
    }`;
    const response = await api.get(modelUrl);
    if (response.status === 200) {
      // 判断如果是字符串，转换为json对象
      if (typeof response.data === "string") {
        console.log("response is string, convert to json", response.data);
        response.data = JSON.parse(response.data);
      }
      this.modelCache[modelType].set(modelName, response.data);
      return JSON.parse(JSON.stringify(response.data));
    } else {
      console.error(`Failed to load model: ${modelName}`, response);
      throw new Error(`Failed to load model: ${modelName}`);
    }
  }

  async saveModel(modelType, modelName, modelData) {
    if (modelName.endsWith(this.modelSuffixMap[modelType])) {
      modelName = modelName.replaceAll("." + this.modelSuffixMap[modelType], "");
    }
    const filename = `/models/${modelType.toLowerCase()}s/${modelName}.${this.modelSuffixMap[modelType]}`;

    // 从缓存中删除
    if (this.modelCache[modelType].has(modelName)) {
      this.modelCache[modelType].delete(modelName);
    }

    // 以formData形式上传数据，后端有一个名称为file的文件接收参数
    const formData = new FormData();
    formData.append("file", new Blob([modelData]), filename);
    return await api.post(UPLOAD_URL, formData);
  }

  async deleteModel(modelType, modelName) {
    if (modelName.endsWith(this.modelSuffixMap[modelType])) {
      modelName = modelName.replaceAll("." + this.modelSuffixMap[modelType], "");
    }

    if (this.modelCache[modelType].has(modelName)) {
      this.modelCache[modelType].delete(modelName);
    }

    const filename = `/models/${modelType.toLowerCase()}s/${modelName}.${this.modelSuffixMap[modelType]}`;
    return api.post(`${DELETE_URL}?filename=${filename}`);
  }

  async listModel(modelType) {
    const res = await api.get("/storage/list?prefix=/models/" + modelType.toLowerCase());

    if (res.status !== 200) {
      console.error("Failed to list models", res);
      return [];
    }
    // res.data 有 private和public两个数组，将它们合并并在每个对象中添加一个visibility字段
    const data = res.data.private.concat(res.data.public).map((item) => {
      item.visibility = item.public ? "公有模型" : "个人模型";
      // 从带path的fileName中提取的文件名
      item.name = item.fileName.split("/").pop();
      return item;
    });

    return data;
  }

  removeModelSuffix(modelName) {
    for (const modelType in this.modelSuffixMap) {
      if (modelName.endsWith(this.modelSuffixMap[modelType])) {
        return modelName.replaceAll("." + this.modelSuffixMap[modelType], "");
      }
    }
    return modelName;
  }
}

// 导出单例实例
/**
 *
 * @type {ModelManager}
 */
export const modelManager = ModelManager.getInstance();

// 使用示例:
// import { modelManager } from './ModelManager.js';
//
// modelManager.getModel('SCENARIO', 'exampleScenario')
//     .then(model => console.log(model))
//     .catch(error => console.error(error));
