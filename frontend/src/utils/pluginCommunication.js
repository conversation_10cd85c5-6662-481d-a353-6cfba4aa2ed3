/**
 * 插件通信管理器
 * 负责处理插件与主应用之间的BroadcastChannel通信
 */

class PluginCommunication {
  constructor() {
    this.channel = null
    this.channelName = null
    this.isConnected = false
    this.messageHandlers = new Map()
    this.pendingMessages = []
    this.config = null
    this.pluginId = null
  }

  /**
   * 初始化通信
   * 从URL参数中获取channel名称并建立连接
   */
  async initialize() {
    try {
      // 从URL参数中获取插件ID/channel名称
      this.pluginId = this.getPluginIdFromUrl()
      
      if (!this.pluginId) {
        throw new Error('未找到插件ID参数，请确保URL中包含pluginId参数')
      }

      this.channelName = this.pluginId
      
      // 创建BroadcastChannel
      this.channel = new BroadcastChannel(this.channelName)
      
      // 设置消息监听器
      this.channel.addEventListener('message', this.handleMessage.bind(this))
      
      console.log(`插件通信已初始化，Channel: ${this.channelName}`)
      
      // 发送hello消息
      await this.sendHelloMessage()
      
      return true
    } catch (error) {
      console.error('插件通信初始化失败:', error)
      throw error
    }
  }

  /**
   * 从URL中获取插件ID
   */
  getPluginIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('pluginId') || urlParams.get('channel') || urlParams.get('id')
  }

  /**
   * 发送Hello消息给主应用
   */
  async sendHelloMessage() {
    const helloMessage = {
      type: 'hello',
      from: 'plugin',
      pluginId: this.pluginId,
      timestamp: Date.now(),
      data: {
        name: 'lantu-plugin-modeler',
        version: '0.0.1',
        description: 'Lantu Modeler Plugin',
        capabilities: ['modeling', 'simulation', 'visualization'],
        url: window.location.href
      }
    }

    return new Promise((resolve, reject) => {
      // 设置超时
      const timeout = setTimeout(() => {
        reject(new Error('Hello消息超时，未收到主应用响应'))
      }, 10000) // 10秒超时

      // 监听配置响应
      const handleConfigResponse = (event) => {
        if (event.data.type === 'config' && event.data.to === 'plugin') {
          clearTimeout(timeout)
          this.channel.removeEventListener('message', handleConfigResponse)
          this.config = event.data.data
          this.isConnected = true
          console.log('收到主应用配置:', this.config)
          resolve(this.config)
        }
      }

      this.channel.addEventListener('message', handleConfigResponse)
      
      // 发送hello消息
      this.sendMessage(helloMessage)
      console.log('已发送Hello消息到主应用')
    })
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(event) {
    const message = event.data
    
    console.log('收到消息:', message)

    // 检查消息格式
    if (!message || !message.type) {
      console.warn('收到无效消息格式:', message)
      return
    }

    // 调用注册的消息处理器
    const handler = this.messageHandlers.get(message.type)
    if (handler) {
      try {
        handler(message)
      } catch (error) {
        console.error(`处理消息类型 ${message.type} 时出错:`, error)
      }
    } else {
      console.warn(`未找到消息类型 ${message.type} 的处理器`)
    }
  }

  /**
   * 发送消息到主应用
   */
  sendMessage(message) {
    if (!this.channel) {
      console.error('通信通道未初始化')
      return false
    }

    // 添加通用字段
    const fullMessage = {
      ...message,
      from: 'plugin',
      pluginId: this.pluginId,
      timestamp: Date.now()
    }

    try {
      this.channel.postMessage(fullMessage)
      console.log('消息已发送:', fullMessage)
      return true
    } catch (error) {
      console.error('发送消息失败:', error)
      return false
    }
  }

  /**
   * 注册消息处理器
   */
  onMessage(type, handler) {
    this.messageHandlers.set(type, handler)
  }

  /**
   * 移除消息处理器
   */
  offMessage(type) {
    this.messageHandlers.delete(type)
  }

  /**
   * 发送数据到主应用
   */
  sendData(type, data) {
    return this.sendMessage({
      type: 'data',
      dataType: type,
      data: data
    })
  }

  /**
   * 请求主应用数据
   */
  async requestData(type, params = {}) {
    return new Promise((resolve, reject) => {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      // 设置超时
      const timeout = setTimeout(() => {
        this.offMessage(`response_${requestId}`)
        reject(new Error(`数据请求超时: ${type}`))
      }, 30000) // 30秒超时

      // 监听响应
      this.onMessage(`response_${requestId}`, (message) => {
        clearTimeout(timeout)
        this.offMessage(`response_${requestId}`)
        
        if (message.success) {
          resolve(message.data)
        } else {
          reject(new Error(message.error || '请求失败'))
        }
      })

      // 发送请求
      this.sendMessage({
        type: 'request',
        requestId: requestId,
        dataType: type,
        params: params
      })
    })
  }

  /**
   * 获取插件配置
   */
  getConfig() {
    return this.config
  }

  /**
   * 获取插件ID
   */
  getPluginId() {
    return this.pluginId
  }

  /**
   * 检查是否已连接
   */
  isReady() {
    return this.isConnected && this.config !== null
  }

  /**
   * 销毁通信连接
   */
  destroy() {
    if (this.channel) {
      this.channel.close()
      this.channel = null
    }
    this.isConnected = false
    this.messageHandlers.clear()
    this.config = null
    console.log('插件通信已销毁')
  }
}

// 创建单例实例
const pluginCommunication = new PluginCommunication()

export default pluginCommunication
