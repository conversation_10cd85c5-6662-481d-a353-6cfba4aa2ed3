/**
 * 插件API封装
 * 提供便于使用的API接口供插件内部调用
 */

import { usePluginStore } from '../stores/plugin.js'
import { getPluginApi } from '../boot/axios.js'

class PluginApi {
  constructor() {
    this.store = null
    this.httpApi = null
  }

  /**
   * 初始化API
   */
  init() {
    this.store = usePluginStore()
    this.httpApi = getPluginApi()
  }

  /**
   * 检查API是否就绪
   */
  isReady() {
    return this.store?.isReady && this.httpApi
  }

  /**
   * 向主应用发送数据
   */
  sendToHost(type, data) {
    if (!this.store) {
      console.error('插件Store未初始化')
      return false
    }
    return this.store.sendData(type, data)
  }

  /**
   * 从主应用请求数据
   */
  async requestFromHost(type, params = {}) {
    if (!this.store) {
      throw new Error('插件Store未初始化')
    }
    return await this.store.requestData(type, params)
  }

  /**
   * 发送自定义消息到主应用
   */
  sendMessage(message) {
    if (!this.store) {
      console.error('插件Store未初始化')
      return false
    }
    return this.store.sendMessage(message)
  }

  /**
   * 通知主应用插件状态变化
   */
  notifyStatusChange(status, data = {}) {
    return this.sendMessage({
      type: 'statusChange',
      data: {
        status,
        ...data
      }
    })
  }

  /**
   * 通知主应用数据变化
   */
  notifyDataChange(dataType, data) {
    return this.sendMessage({
      type: 'dataChange',
      data: {
        dataType,
        data
      }
    })
  }

  /**
   * 请求主应用打开文件
   */
  async requestOpenFile(fileType = 'any', options = {}) {
    return await this.requestFromHost('openFile', {
      fileType,
      ...options
    })
  }

  /**
   * 请求主应用保存文件
   */
  async requestSaveFile(data, fileName, fileType = 'json') {
    return await this.requestFromHost('saveFile', {
      data,
      fileName,
      fileType
    })
  }

  /**
   * 获取主应用用户信息
   */
  async getUserInfo() {
    return await this.requestFromHost('userInfo')
  }

  /**
   * 获取主应用项目信息
   */
  async getProjectInfo() {
    return await this.requestFromHost('projectInfo')
  }

  // HTTP API 方法

  /**
   * GET请求
   */
  async get(url, config = {}) {
    if (!this.httpApi) {
      throw new Error('HTTP API未初始化')
    }
    const response = await this.httpApi.get(url, config)
    return response.data
  }

  /**
   * POST请求
   */
  async post(url, data = {}, config = {}) {
    if (!this.httpApi) {
      throw new Error('HTTP API未初始化')
    }
    const response = await this.httpApi.post(url, data, config)
    return response.data
  }

  /**
   * PUT请求
   */
  async put(url, data = {}, config = {}) {
    if (!this.httpApi) {
      throw new Error('HTTP API未初始化')
    }
    const response = await this.httpApi.put(url, data, config)
    return response.data
  }

  /**
   * DELETE请求
   */
  async delete(url, config = {}) {
    if (!this.httpApi) {
      throw new Error('HTTP API未初始化')
    }
    const response = await this.httpApi.delete(url, config)
    return response.data
  }

  /**
   * 上传文件
   */
  async uploadFile(url, file, onProgress = null) {
    if (!this.httpApi) {
      throw new Error('HTTP API未初始化')
    }

    const formData = new FormData()
    formData.append('file', file)

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }

    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percentCompleted)
      }
    }

    const response = await this.httpApi.post(url, formData, config)
    return response.data
  }

  /**
   * 下载文件
   */
  async downloadFile(url, fileName) {
    if (!this.httpApi) {
      throw new Error('HTTP API未初始化')
    }

    const response = await this.httpApi.get(url, {
      responseType: 'blob'
    })

    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)

    return true
  }

  /**
   * 获取插件配置
   */
  getConfig() {
    return this.store?.config || null
  }

  /**
   * 获取插件ID
   */
  getPluginId() {
    return this.store?.pluginId || null
  }

  /**
   * 获取API基础URL
   */
  getApiBase() {
    return this.store?.apiBase || ''
  }

  /**
   * 检查是否有认证token
   */
  hasToken() {
    return this.store?.hasToken || false
  }
}

// 创建单例实例
const pluginApi = new PluginApi()

export default pluginApi

// 导出便捷方法
export const {
  sendToHost,
  requestFromHost,
  sendMessage,
  notifyStatusChange,
  notifyDataChange,
  requestOpenFile,
  requestSaveFile,
  getUserInfo,
  getProjectInfo,
  get,
  post,
  put,
  delete: del,
  uploadFile,
  downloadFile,
  getConfig,
  getPluginId,
  getApiBase,
  hasToken
} = pluginApi
