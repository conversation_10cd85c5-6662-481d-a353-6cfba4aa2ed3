<template>
  <router-view />
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { usePluginStore } from './stores/plugin.js'

const pluginStore = usePluginStore()

onMounted(async () => {
  try {
    console.log('开始初始化插件...')

    // 初始化插件通信和HTTP API
    await pluginStore.initialize()

    console.log('插件初始化完成')
  } catch (error) {
    console.error('插件初始化失败:', error)
    // 可以在这里显示错误提示给用户
  }
})

onUnmounted(() => {
  // 清理资源
  pluginStore.destroy()
})
</script>
