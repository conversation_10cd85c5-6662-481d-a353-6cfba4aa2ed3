<template>
  <q-dialog
    v-model="editorStore.attributeDialog.visible"
    persistent
  >
    <q-card class="attribute-dialog">
      <q-card-section class="row dialog-header">
        <span>{{ editorStore.attributeDialog.title }}</span>
        <q-space />
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closeAttributeDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-body">
        <!-- 基础属性配置 -->
        <div class="basic-attributes">
          <div class="section-title">基础配置</div>
          <div class="attribute-form">
            <q-input
              v-if="editorStore.attributeDialog.data.name !== undefined"
              v-model="editorStore.attributeDialog.data.name"
              label="模块名称"
              outlined
              dense
              class="form-field"
            />

            <q-input
              v-if="editorStore.attributeDialog.data.type"
              :model-value="getModuleTypeName(editorStore.attributeDialog.data.type)"
              label="模块类型"
              outlined
              dense
              readonly
              class="form-field"
            />

            <div
              v-if="ModuleConfig[editorStore.attributeDialog.data.type]?.supportProcessModel"
              class="form-field"
            >
              <q-select
                v-model="selectedProcessModel"
                :options="processModelOptions"
                label="进程模型"
                outlined
                dense
                emit-value
                map-options
                @update:model-value="onProcessModelChange"
              >
                <template v-slot:no-option>
                  <q-item>
                    <q-item-section class="text-grey">
                      暂无可用的进程模型
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
          </div>
        </div>

        <q-separator class="section-separator" />

        <!-- 属性配置树 -->
        <div class="attribute-configuration">
          <div class="section-title">属性配置</div>
          <div class="attribute-tree-container">
            <div
              v-if="!currentProcessModelAttributes || currentProcessModelAttributes.length === 0"
              class="no-attributes-message"
            >
              <q-icon name="info" size="md" class="q-mr-sm" />
              <span>请选择进程模型以配置相关属性</span>
            </div>
            <AttributeTree
              v-else
              :attributes="currentProcessModelAttributes"
              :values="editorStore.attributeDialog.data.attributeValues || {}"
              @update:values="onAttributeValuesUpdate"
            />
          </div>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closeAttributeDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="applyAndCloseAttributes"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useEditorStore } from 'stores/editor'
import { getModuleTypeName, ModuleConfig } from 'src/utils/modules.js'
import AttributeTree from './AttributeTree.vue'

const editorStore = useEditorStore()

// 当前选择的进程模型
const selectedProcessModel = ref('')

// 进程模型选项（暂时为空，等待模型管理器实现）
const processModelOptions = computed(() => {
  // TODO: 从模型管理器获取可用的进程模型列表
  return [
    { label: '无', value: '' },
    { label: 'IPv4 协议模型', value: 'ipv4' },
    { label: 'TCP 协议模型', value: 'tcp' },
    { label: 'UDP 协议模型', value: 'udp' }
  ]
})

// 当前进程模型的属性定义
const currentProcessModelAttributes = computed(() => {
  if (!selectedProcessModel.value) {
    return []
  }

  // TODO: 从模型管理器根据进程模型名称获取属性定义
  // 这里暂时返回一个示例结构，基于 ipv4.pr.m 的格式
  if (selectedProcessModel.value === 'ipv4') {
    return getMockIPv4Attributes()
  }

  return []
})

// 监听对话框打开，初始化选择的进程模型
watch(() => editorStore.attributeDialog.visible, (visible) => {
  if (visible) {
    selectedProcessModel.value = editorStore.attributeDialog.data.model || ''
  }
})

const closeAttributeDialog = () => {
  editorStore.attributeDialog.visible = false
  editorStore.attributeDialog.data = {}
  editorStore.attributeDialog.node = null
}

const onProcessModelChange = (modelName) => {
  editorStore.attributeDialog.data.model = modelName
  // 清空之前的属性值，因为不同模型的属性结构可能不同
  editorStore.attributeDialog.data.attributeValues = {}
}

const onAttributeValuesUpdate = (newValues) => {
  editorStore.attributeDialog.data.attributeValues = newValues
}

const applyAndCloseAttributes = () => {
  // 应用属性到节点
  if (editorStore.attributeDialog.node) {
    editorStore.attributeDialog.node.label = editorStore.attributeDialog.data.name;
    editorStore.attributeDialog.node.setData(editorStore.attributeDialog.data);
    editorStore.hasUnsavedChanges = true;
  }
  closeAttributeDialog()
}

// 临时的 IPv4 模型属性定义（基于 ipv4.pr.m）
function getMockIPv4Attributes() {
  return [
    {
      name: "routing_enabled",
      label: "启用路由功能",
      type: "bool",
      desc: "是否启用IP路由转发功能",
      defaultValue: false,
      symbolMap: [
        { key: "启用", value: true },
        { key: "禁用", value: false }
      ]
    },
    {
      name: "processing_rate",
      label: "处理速率",
      type: "int",
      desc: "IP数据包处理速率，单位：pkt/s",
      unit: "pkt/s",
      defaultValue: 1000000
    },
    {
      name: "gateway",
      label: "默认网关",
      type: "string",
      desc: "默认网关IP地址",
      defaultValue: ""
    },
    {
      name: "interfaces",
      label: "网络接口配置",
      type: "array",
      desc: "物理网络接口配置列表",
      defaultValue: [],
      elementType: {
        type: "object",
        children: [
          {
            name: "name",
            label: "接口名称",
            type: "string",
            desc: "网络接口名称",
            defaultValue: "IF0"
          },
          {
            name: "ip_address",
            label: "IP地址",
            type: "string",
            desc: "接口IP地址",
            defaultValue: "***********"
          },
          {
            name: "mask",
            label: "子网掩码",
            type: "string",
            desc: "子网掩码，格式如*************",
            defaultValue: "*************"
          },
          {
            name: "mtu",
            label: "MTU",
            type: "int",
            desc: "最大传输单元，单位：字节",
            defaultValue: 1500
          }
        ]
      }
    }
  ]
}
</script>

<style lang="scss" scoped>
.attribute-dialog {
  width: 800px;
  max-width: 90vw;
  max-height: 80vh;

  .dialog-header {
    padding-bottom: 0;
  }

  .dialog-body {
    max-height: 60vh;
    overflow-y: auto;
    padding-top: 0;

    .basic-attributes {
      margin-bottom: 0;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
      }

      .attribute-form {
        .form-field {
          margin-bottom: 8px;
        }
      }
    }

    .section-separator {
      margin: 12px 0;
    }

    .attribute-configuration {
      .section-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 16px;
        color: #333;
      }

      .attribute-tree-container {
        min-height: 200px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 16px;
        background: #fafafa;

        .no-attributes-message {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100px;
          color: #666;
          font-style: italic;
        }
      }
    }
  }

  .dialog-footer {
    padding: 16px 24px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
  }
}
</style>
