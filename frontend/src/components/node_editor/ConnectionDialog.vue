<template>
  <q-dialog
    v-model="editorStore.connectionDialog.visible"
    persistent
  >
    <q-card class="connection-dialog">
      <q-card-section class="row">
          <span>{{ editorStore.connectionDialog.title }}</span>
          <q-space />
          <q-btn
            flat
            dense
            round
            icon="close"
            @click="closeConnectionDialog"
          />

      </q-card-section>

      <q-card-section class="dialog-body">
        <div class="property-form">
          <q-input
            :model-value="getConnectionTypeName(editorStore.connectionDialog.data.type)"
            label="连接类型"
            outlined
            dense
            readonly
            class="form-field"
          />

          <q-input
            :model-value="sourceModule"
            label="源模块"
            outlined
            dense
            readonly
            class="form-field"
          />

          <q-select
            v-model="editorStore.connectionDialog.data.portIndices.srcIdx"
            :options="sourcePortOptions"
            label="源端口索引"
            outlined
            dense
            options-dense
            emit-value
            map-options
            class="form-field"
          />

          <q-input
            :model-value="targetModule"
            label="目标模块"
            outlined
            dense
            readonly
            class="form-field"
          />

          <q-select
            v-model="editorStore.connectionDialog.data.portIndices.dstIdx"
            :options="targetPortOptions"
            label="目标端口索引"
            outlined
            dense
            options-dense
            emit-value
            map-options
            class="form-field"
          />

          <div class="form-field-info">
            <q-card flat bordered>
              <q-card-section>
                <div class="text-subtitle2">说明：</div>
                <ul class="info-list">
                  <li>端口索引用于标识模块的输入/输出端口</li>
                  <li>每个模块的同类型端口索引必须唯一</li>
                  <li>已使用的端口索引不会显示在选项中</li>
                </ul>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closeConnectionDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="applyConnectionProperties"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { useEditorStore } from 'stores/editor'
import {getConnectionTypeName} from 'src/utils/modules.js'


const editorStore = useEditorStore()

const sourceModule = computed(() => {
  return editorStore.connectionDialog.edge.getSourceNode().getData().name;
})

const targetModule = computed(() => {
  return editorStore.connectionDialog.edge.getTargetNode().getData().name;
})


const sourcePortOptions = computed(() => {
  const availablePortsWithCurrent = editorStore.connectionDialog.availableSourcePorts.includes(editorStore.connectionDialog.data.portIndices.srcIdx)
    ? editorStore.connectionDialog.availableSourcePorts
    : editorStore.connectionDialog.availableSourcePorts.concat([editorStore.connectionDialog.data.portIndices.srcIdx])

  availablePortsWithCurrent.sort();
  return availablePortsWithCurrent.map(port => ({
    label: `端口 ${port}`,
    value: port
  }))
})

const targetPortOptions = computed(() => {
  const availablePortsWithCurrent = editorStore.connectionDialog.availableTargetPorts.includes(editorStore.connectionDialog.data.portIndices.dstIdx)
    ? editorStore.connectionDialog.availableTargetPorts
    : editorStore.connectionDialog.availableTargetPorts.concat([editorStore.connectionDialog.data.portIndices.dstIdx])
  availablePortsWithCurrent.sort();
  return availablePortsWithCurrent.map(port => ({
    label: `端口 ${port}`,
    value: port
  }))
})

const closeConnectionDialog = () => {
  editorStore.connectionDialog.visible = false
}

const applyConnectionProperties = () => {
  editorStore.connectionDialog.edge.setData(editorStore.connectionDialog.data)

  closeConnectionDialog()
}
</script>

<style lang="scss" scoped>
.connection-dialog {
  width: 500px;
  max-width: 90vw;

  .dialog-body {
    padding: 24px;

    .property-form {
      .form-field {
        margin-bottom: 16px;
      }

      .form-field-info {
        margin-top: 24px;

        .info-list {
          margin: 8px 0 0 16px;
          padding: 0;

          li {
            margin-bottom: 4px;
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }

  .dialog-footer {
    padding: 16px 24px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
  }
}
</style>
