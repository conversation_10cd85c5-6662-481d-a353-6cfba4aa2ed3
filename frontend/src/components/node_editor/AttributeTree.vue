<template>
  <q-tree
    :nodes="treeData"
    node-key="nodeKey"
    label-key="label"
    dense
    no-nodes-label="暂无属性配置"
  >
    <template v-slot:default-header="prop">
      <div class="row full-width items-center q-gutter-x-md">
        <div class="col-4 q-ma-none">
          <q-icon :name="prop.node.icon" size="sm" class="q-mr-sm" />
          {{ prop.node.label }}
        </div>

        <template v-if="prop.node.attr">
          <div class="col-1">{{ prop.node.attr.type }}</div>
          <div class="col">
            <q-input
              v-if="['string', 'int', 'float'].includes(prop.node.attr.type)"
              v-model="prop.node.value"
              :type="prop.node.attr.type === 'string' ? 'text' : 'number'"
              dense
              outlined
              @update:model-value="(val) => updateAttributeValue(prop.node.attr.path, val)"
              :suffix="prop.node.attr.unit"
            />
            <q-select
              v-else-if="prop.node.attr.type === 'option'"
              v-model="prop.node.value"
              :options="prop.node.attr.options"
              option-label="key"
              option-value="value"
              emit-value
              map-options
              outlined
              options-dense
              dense
              @update:model-value="(val) => updateAttributeValue(prop.node.attr.path, val)"
            />
            <q-toggle
              v-else-if="prop.node.attr.type === 'bool'"
              v-model="prop.node.value"
              dense
              @update:model-value="(val) => updateAttributeValue(prop.node.attr.path, val)"
            />
            <div v-else-if="prop.node.attr.type === 'array'" class="row items-center">
              <q-btn flat round dense icon="add" @click.stop="addArrayItem(prop.node)" />
              <span class="q-ml-sm">{{ (prop.node.value || []).length }} 项</span>
            </div>
          </div>
          <!-- Action buttons -->
          <div class="col-1">
            <div class="row items-center q-gutter-x-xs">
              <q-btn
                v-if="prop.node.attr.symbolMap && prop.node.attr.symbolMap.length"
                flat
                round
                dense
                size="sm"
                color="primary"
                icon="list"
                @click.stop="showSymbolMapDialog(prop.node.attr)"
              >
                <q-tooltip>预设值</q-tooltip>
              </q-btn>
              <q-btn
                v-if="prop.node.attr.isArrayItem"
                flat
                round
                dense
                size="sm"
                color="negative"
                icon="remove"
                @click.stop="removeArrayItem(prop.node)"
              >
                <q-tooltip>删除项</q-tooltip>
              </q-btn>
              <q-btn
                flat
                round
                dense
                size="sm"
                color="primary"
                icon="restore"
                @click.stop="resetToDefault(prop.node)"
              >
                <q-tooltip>恢复默认值</q-tooltip>
              </q-btn>
            </div>
          </div>
        </template>
      </div>
    </template>
  </q-tree>
</template>

<script setup>
import { computed } from 'vue'
import {uid, useQuasar} from 'quasar'

const props = defineProps({
  attributes: {
    type: Array,
    required: true
  },
  values: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:values'])

const $q = useQuasar()

// 构建树形数据
const treeData = computed(() => {
  if (!props.attributes) return []
  return props.attributes.map(attr => buildAttributeNode(attr)).filter(node => node !== null)
})

// 构建属性节点（递归函数）
function buildAttributeNode(attr, parentPath = '') {
  // 检查属性是否设置为隐藏
  if (attr.hidden && attr.hidden === true) {
    return null
  }

  const currentPath = parentPath ? `${parentPath}.${attr.name}` : attr.name
  const value = getAttributeValue(currentPath)

  const node = {
    label: attr.label || attr.name,
    nodeKey: currentPath, // 添加唯一的节点键
    icon: getAttributeIcon(attr.type),
    attr: { ...attr, path: currentPath },
    value,
    children: []
  }

  if (attr.type === 'object' && attr.children) {
    // 过滤掉hidden为true的子属性
    node.children = attr.children
      .map(child => buildAttributeNode(child, currentPath))
      .filter(childNode => childNode !== null)
  } else if (attr.type === 'array' && attr.elementType) {
    const arrayValue = value || []
    node.children = arrayValue.map((_, index) => ({
      label: `[${index}]`,
      nodeKey: `${currentPath}[${index}]`, // 添加唯一的节点键
      icon: getAttributeIcon(attr.elementType.type),
      attr: {
        ...attr.elementType,
        path: `${currentPath}[${index}]`,
        isArrayItem: true,
        arrayIndex: index
      },
      value: arrayValue[index],
      children: attr.elementType.type === 'object' && attr.elementType.children
        ? attr.elementType.children.map(child => buildAttributeNode(child, `${currentPath}[${index}]`))
        : []
    }))
  }

  return node
}

// 获取属性值
function getAttributeValue(path) {
  if (!path) return undefined
  const pathParts = path.split('.')
  const currentValue = getNestedValue(props.values || {}, pathParts)
  if (currentValue !== undefined) return currentValue
  return findDefaultValue(props.attributes || [], pathParts)
}

// 查找默认值
function findDefaultValue(attributes, pathParts) {
  if (!attributes || !Array.isArray(attributes) || !pathParts || pathParts.length === 0) {
    return undefined
  }

  const [current, ...rest] = pathParts
  const arrayMatch = current.match(/(\w+)\[(\d+)\]/)
  const attrName = arrayMatch ? arrayMatch[1] : current

  const attribute = attributes.find(attr => attr && attr.name === attrName)
  if (!attribute) return undefined

  if (rest.length === 0) return attribute.defaultValue

  if (attribute.type === 'object' && attribute.children) {
    return findDefaultValue(attribute.children, rest)
  } else if (attribute.type === 'array' && attribute.elementType) {
    return attribute.elementType.defaultValue
  }

  return undefined
}

// 更新属性值
function updateAttributeValue(path, value) {
  const newValues = { ...props.values }
  setNestedValue(newValues, path.split('.'), value)
  emit('update:values', newValues)
}

// 获取属性图标
function getAttributeIcon(type) {
  const iconMap = {
    string: 'text_fields',
    int: 'numbers',
    float: 'numbers',
    bool: 'toggle_on',
    option: 'list',
    object: 'folder',
    array: 'view_list'
  }
  return iconMap[type] || 'help_outline'
}

// 重置为默认值
function resetToDefault(node) {
  const { path } = node.attr
  const newValues = { ...props.values }
  deleteNestedValue(newValues, path.split('.'))
  emit('update:values', newValues)
}

// 添加数组项
function addArrayItem(node) {
  const { path } = node.attr
  const currentValue = getAttributeValue(path) || []
  const newValues = { ...props.values }
  setNestedValue(newValues, path.split('.'), [...currentValue, null])
  emit('update:values', newValues)
}

// 删除数组项
function removeArrayItem(node) {
  const { path, arrayIndex } = node.attr
  const parentPath = path.split('[')[0]
  const currentValue = getAttributeValue(parentPath) || []
  currentValue.splice(arrayIndex, 1)

  const newValues = { ...props.values }
  setNestedValue(newValues, parentPath.split('.'), currentValue)
  emit('update:values', newValues)
}

// 显示符号映射对话框
const showSymbolMapDialog = (attr) => {
  $q.dialog({
    title: '选择预设值',
    message: `为属性 "${attr.label}" 选择预设值：`,
    options: {
      type: 'radio',
      items: attr.symbolMap.map(item => ({
        label: `${item.key}  【${item.value}】`,
        value: item.value
      }))
    },
    cancel: '取消'
  }).onOk((val) => {
    updateAttributeValue(attr.path, val)
  })
}

// 辅助函数
function getNestedValue(obj, pathParts) {
  return pathParts.reduce((acc, part) => {
    if (acc === undefined || acc === null) return undefined
    const arrayMatch = part.match(/(\w+)\[(\d+)\]/)
    if (arrayMatch) {
      const [, arrayName, index] = arrayMatch
      return acc[arrayName]?.[parseInt(index)]
    }
    return acc[part]
  }, obj)
}

function setNestedValue(obj, pathParts, value) {
  const lastPart = pathParts.pop()
  let current = obj

  for (const part of pathParts) {
    const arrayMatch = part.match(/(\w+)\[(\d+)\]/)
    if (arrayMatch) {
      const [, arrayName, index] = arrayMatch
      current[arrayName] = current[arrayName] || []
      current = current[arrayName][parseInt(index)] = current[arrayName][parseInt(index)] || {}
    } else {
      current = current[part] = current[part] || {}
    }
  }

  current[lastPart] = value
}

function deleteNestedValue(obj, pathParts) {
  const lastPart = pathParts.pop()
  let current = obj

  for (const part of pathParts) {
    const arrayMatch = part.match(/(\w+)\[(\d+)\]/)
    if (arrayMatch) {
      const [, arrayName, index] = arrayMatch
      if (!current[arrayName]?.[parseInt(index)]) return
      current = current[arrayName][parseInt(index)]
    } else {
      if (!current[part]) return
      current = current[part]
    }
  }

  delete current[lastPart]
}

</script>

<style scoped>
.q-tree__node-header {
  min-height: 48px;
}

.q-tree__node-header-content {
  width: 100%;
}
</style>
