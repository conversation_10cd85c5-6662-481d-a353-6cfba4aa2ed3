<template>
  <div class="plugin-example">
    <h3>插件通信示例</h3>
    
    <!-- 插件状态显示 -->
    <div class="status-section">
      <h4>插件状态</h4>
      <p>插件ID: {{ pluginStore.pluginId }}</p>
      <p>连接状态: {{ pluginStore.isConnected ? '已连接' : '未连接' }}</p>
      <p>就绪状态: {{ pluginStore.isReady ? '就绪' : '未就绪' }}</p>
      <p>API基础URL: {{ pluginStore.apiBase }}</p>
      <p>是否有Token: {{ pluginStore.hasToken ? '是' : '否' }}</p>
    </div>

    <!-- 发送消息到主应用 -->
    <div class="message-section">
      <h4>发送消息到主应用</h4>
      <q-input 
        v-model="messageType" 
        label="消息类型" 
        outlined 
        dense 
      />
      <q-input 
        v-model="messageData" 
        label="消息数据 (JSON)" 
        type="textarea" 
        outlined 
        dense 
      />
      <q-btn 
        @click="sendMessageToHost" 
        label="发送消息" 
        color="primary" 
        :disable="!pluginStore.isReady"
      />
    </div>

    <!-- 请求主应用数据 -->
    <div class="request-section">
      <h4>请求主应用数据</h4>
      <q-input 
        v-model="requestType" 
        label="请求类型" 
        outlined 
        dense 
      />
      <q-btn 
        @click="requestDataFromHost" 
        label="请求数据" 
        color="secondary" 
        :disable="!pluginStore.isReady"
        :loading="requesting"
      />
      <div v-if="requestResult" class="request-result">
        <h5>请求结果:</h5>
        <pre>{{ JSON.stringify(requestResult, null, 2) }}</pre>
      </div>
    </div>

    <!-- HTTP API 测试 -->
    <div class="api-section">
      <h4>HTTP API 测试</h4>
      <q-input 
        v-model="apiUrl" 
        label="API路径" 
        outlined 
        dense 
        placeholder="/api/test"
      />
      <q-btn 
        @click="testHttpApi" 
        label="测试GET请求" 
        color="accent" 
        :disable="!pluginStore.isReady"
        :loading="apiTesting"
      />
      <div v-if="apiResult" class="api-result">
        <h5>API结果:</h5>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </div>

    <!-- 错误显示 -->
    <div v-if="error" class="error-section">
      <q-banner class="bg-negative text-white">
        <template v-slot:avatar>
          <q-icon name="error" />
        </template>
        {{ error }}
      </q-banner>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { usePluginStore } from '../stores/plugin.js'
import pluginApi from '../utils/pluginApi.js'

const pluginStore = usePluginStore()

// 消息发送
const messageType = ref('test')
const messageData = ref('{"message": "Hello from plugin"}')

// 数据请求
const requestType = ref('userInfo')
const requesting = ref(false)
const requestResult = ref(null)

// HTTP API测试
const apiUrl = ref('/api/test')
const apiTesting = ref(false)
const apiResult = ref(null)

// 错误信息
const error = ref('')

/**
 * 发送消息到主应用
 */
const sendMessageToHost = () => {
  try {
    let data
    try {
      data = JSON.parse(messageData.value)
    } catch {
      data = messageData.value
    }

    const success = pluginApi.sendToHost(messageType.value, data)
    if (success) {
      error.value = ''
      console.log('消息发送成功')
    } else {
      error.value = '消息发送失败'
    }
  } catch (err) {
    error.value = `发送消息时出错: ${err.message}`
  }
}

/**
 * 请求主应用数据
 */
const requestDataFromHost = async () => {
  requesting.value = true
  requestResult.value = null
  error.value = ''

  try {
    const result = await pluginApi.requestFromHost(requestType.value)
    requestResult.value = result
  } catch (err) {
    error.value = `请求数据失败: ${err.message}`
  } finally {
    requesting.value = false
  }
}

/**
 * 测试HTTP API
 */
const testHttpApi = async () => {
  apiTesting.value = true
  apiResult.value = null
  error.value = ''

  try {
    const result = await pluginApi.get(apiUrl.value)
    apiResult.value = result
  } catch (err) {
    error.value = `API请求失败: ${err.message}`
  } finally {
    apiTesting.value = false
  }
}
</script>

<style scoped>
.plugin-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.status-section,
.message-section,
.request-section,
.api-section,
.error-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.request-result,
.api-result {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.request-result pre,
.api-result pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.q-input,
.q-btn {
  margin-bottom: 10px;
}
</style>
