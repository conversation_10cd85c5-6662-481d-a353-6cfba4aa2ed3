import { defineStore } from 'pinia'
import pluginCommunication from '../utils/pluginCommunication.js'
import { createPluginApi } from '../boot/axios.js'

/**
 * 插件状态管理
 * 使用Pinia管理插件的配置、连接状态等信息
 */
export const usePluginStore = defineStore('plugin', {
  state: () => ({
    isInitialized: false,
    isConnected: false,
    config: null,
    pluginId: null,
    error: null,
    loading: false
  }),

  getters: {
    isReady: (state) => state.isInitialized && state.isConnected && state.config !== null,
    apiBase: (state) => state.config?.apiBase || '',
    token: (state) => state.config?.token || '',
    hasToken: (state) => !!state.token
  },

  actions: {

    async initialize() {
      if (this.isInitialized) {
        console.warn('插件已经初始化过了')
        return
      }

      this.loading = true
      this.error = null

      try {
        // 初始化通信
        await pluginCommunication.initialize()

        // 获取配置信息
        const receivedConfig = pluginCommunication.getConfig()

        // 更新状态
        this.config = receivedConfig
        this.pluginId = pluginCommunication.getPluginId()
        this.isConnected = pluginCommunication.isReady()
        this.isInitialized = true

        // 创建HTTP API实例
        if (receivedConfig) {
          createPluginApi(receivedConfig)
        }

        console.log('插件Store初始化成功:', {
          pluginId: this.pluginId,
          config: this.config,
          isConnected: this.isConnected
        })

        // 注册通信事件监听器
        this.setupMessageHandlers()

      } catch (err) {
        this.error = err.message || '插件初始化失败'
        console.error('插件Store初始化失败:', err)
        throw err
      } finally {
        this.loading = false
      }
    },

    setupMessageHandlers() {
      // 监听配置更新
      pluginCommunication.onMessage('configUpdate', (message) => {
        if (message.data) {
          this.config = { ...this.config, ...message.data }
          console.log('配置已更新:', this.config)
        }
      })

      // 监听连接状态变化
      pluginCommunication.onMessage('connectionStatus', (message) => {
        this.isConnected = message.data?.connected || false
        console.log('连接状态变化:', this.isConnected)
      })

      // 监听错误消息
      pluginCommunication.onMessage('error', (message) => {
        this.error = message.data?.message || '未知错误'
        console.error('收到错误消息:', this.error)
      })
    },

    updateConfig(newConfig) {
      this.config = { ...this.config, ...newConfig }
    },

    sendData(type, data) {
      if (!this.isReady) {
        console.warn('插件未就绪，无法发送数据')
        return false
      }
      return pluginCommunication.sendData(type, data)
    },

    async requestData(type, params = {}) {
      if (!this.isReady) {
        throw new Error('插件未就绪，无法请求数据')
      }
      return await pluginCommunication.requestData(type, params)
    },

    sendMessage(message) {
      if (!this.isReady) {
        console.warn('插件未就绪，无法发送消息')
        return false
      }
      return pluginCommunication.sendMessage(message)
    },

    reset() {
      this.isInitialized = false
      this.isConnected = false
      this.config = null
      this.pluginId = null
      this.error = null
      this.loading = false
    },

    destroy() {
      pluginCommunication.destroy()
      this.reset()
    }
  }
})


