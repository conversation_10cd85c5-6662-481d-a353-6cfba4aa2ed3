import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import pluginCommunication from '../utils/pluginCommunication.js'

/**
 * 插件状态管理
 * 使用Pinia管理插件的配置、连接状态等信息
 */
export const usePluginStore = defineStore('plugin', () => {
  // 状态
  const isInitialized = ref(false)
  const isConnected = ref(false)
  const config = ref(null)
  const pluginId = ref(null)
  const error = ref(null)
  const loading = ref(false)

  // 计算属性
  const isReady = computed(() => isInitialized.value && isConnected.value && config.value !== null)
  
  const apiBase = computed(() => config.value?.apiBase || '')
  
  const token = computed(() => config.value?.token || '')
  
  const hasToken = computed(() => !!token.value)

  // 方法
  const initialize = async () => {
    if (isInitialized.value) {
      console.warn('插件已经初始化过了')
      return
    }

    loading.value = true
    error.value = null

    try {
      // 初始化通信
      await pluginCommunication.initialize()
      
      // 获取配置信息
      const receivedConfig = pluginCommunication.getConfig()
      
      // 更新状态
      config.value = receivedConfig
      pluginId.value = pluginCommunication.getPluginId()
      isConnected.value = pluginCommunication.isReady()
      isInitialized.value = true

      console.log('插件Store初始化成功:', {
        pluginId: pluginId.value,
        config: config.value,
        isConnected: isConnected.value
      })

      // 注册通信事件监听器
      setupMessageHandlers()

    } catch (err) {
      error.value = err.message || '插件初始化失败'
      console.error('插件Store初始化失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const setupMessageHandlers = () => {
    // 监听配置更新
    pluginCommunication.onMessage('configUpdate', (message) => {
      if (message.data) {
        config.value = { ...config.value, ...message.data }
        console.log('配置已更新:', config.value)
      }
    })

    // 监听连接状态变化
    pluginCommunication.onMessage('connectionStatus', (message) => {
      isConnected.value = message.data?.connected || false
      console.log('连接状态变化:', isConnected.value)
    })

    // 监听错误消息
    pluginCommunication.onMessage('error', (message) => {
      error.value = message.data?.message || '未知错误'
      console.error('收到错误消息:', error.value)
    })
  }

  const updateConfig = (newConfig) => {
    config.value = { ...config.value, ...newConfig }
  }

  const sendData = (type, data) => {
    if (!isReady.value) {
      console.warn('插件未就绪，无法发送数据')
      return false
    }
    return pluginCommunication.sendData(type, data)
  }

  const requestData = async (type, params = {}) => {
    if (!isReady.value) {
      throw new Error('插件未就绪，无法请求数据')
    }
    return await pluginCommunication.requestData(type, params)
  }

  const sendMessage = (message) => {
    if (!isReady.value) {
      console.warn('插件未就绪，无法发送消息')
      return false
    }
    return pluginCommunication.sendMessage(message)
  }

  const reset = () => {
    isInitialized.value = false
    isConnected.value = false
    config.value = null
    pluginId.value = null
    error.value = null
    loading.value = false
  }

  const destroy = () => {
    pluginCommunication.destroy()
    reset()
  }

  return {
    // 状态
    isInitialized,
    isConnected,
    config,
    pluginId,
    error,
    loading,
    
    // 计算属性
    isReady,
    apiBase,
    token,
    hasToken,
    
    // 方法
    initialize,
    updateConfig,
    sendData,
    requestData,
    sendMessage,
    reset,
    destroy
  }
})
