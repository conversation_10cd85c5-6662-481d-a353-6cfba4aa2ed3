<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟主应用 - Host</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .plugin-item {
            background: #f9f9f9;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .message-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .message-item {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .message-received {
            background: #d4edda;
            border-left: 3px solid #28a745;
        }
        .message-sent {
            background: #d1ecf1;
            border-left: 3px solid #17a2b8;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input, select {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 模拟主应用 (Host)</h1>
        
        <div class="section">
            <h3>插件管理</h3>
            <button onclick="openPlugin()">打开插件</button>
            <button onclick="generatePluginId()">生成新插件ID</button>
            <div>
                <label>插件ID: </label>
                <input type="text" id="pluginId" value="" readonly>
                <button onclick="copyPluginId()">复制</button>
            </div>
            <div id="pluginStatus" class="status disconnected">
                未连接插件
            </div>
        </div>

        <div class="section">
            <h3>已连接的插件</h3>
            <div id="connectedPlugins">
                <p>暂无插件连接</p>
            </div>
        </div>

        <div class="section">
            <h3>发送消息到插件</h3>
            <div>
                <select id="messageType">
                    <option value="configUpdate">配置更新</option>
                    <option value="connectionStatus">连接状态</option>
                    <option value="customMessage">自定义消息</option>
                </select>
                <input type="text" id="messageData" placeholder="消息内容 (JSON格式)" style="width: 300px;">
                <button onclick="sendMessageToPlugin()">发送消息</button>
            </div>
        </div>

        <div class="section">
            <h3>通信日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="messageLog" class="message-log"></div>
        </div>
    </div>

    <script>
        let currentChannel = null;
        let connectedPlugins = new Map();
        
        // 生成随机插件ID
        function generatePluginId() {
            const id = 'plugin_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
            document.getElementById('pluginId').value = id;
            return id;
        }

        // 复制插件ID
        function copyPluginId() {
            const pluginIdInput = document.getElementById('pluginId');
            pluginIdInput.select();
            document.execCommand('copy');
            alert('插件ID已复制到剪贴板');
        }

        // 打开插件
        function openPlugin() {
            const pluginId = document.getElementById('pluginId').value || generatePluginId();
            const pluginUrl = `http://localhost:9000/?pluginId=${pluginId}`;
            
            // 建立通信通道
            setupPluginCommunication(pluginId);
            
            // 打开插件窗口
            window.open(pluginUrl, '_blank', 'width=1200,height=800');
            
            logMessage('系统', `打开插件: ${pluginUrl}`, 'sent');
        }

        // 建立插件通信
        function setupPluginCommunication(pluginId) {
            if (currentChannel) {
                currentChannel.close();
            }

            currentChannel = new BroadcastChannel(pluginId);
            
            currentChannel.addEventListener('message', (event) => {
                handlePluginMessage(event.data, pluginId);
            });

            logMessage('系统', `建立通信通道: ${pluginId}`, 'sent');
        }

        // 处理插件消息
        function handlePluginMessage(message, pluginId) {
            logMessage('插件', JSON.stringify(message, null, 2), 'received');

            switch (message.type) {
                case 'hello':
                    handleHelloMessage(message, pluginId);
                    break;
                case 'data':
                    handleDataMessage(message);
                    break;
                case 'request':
                    handleRequestMessage(message);
                    break;
                default:
                    console.log('收到未知类型消息:', message);
            }
        }

        // 处理Hello消息
        function handleHelloMessage(message, pluginId) {
            // 记录插件连接
            connectedPlugins.set(pluginId, {
                name: message.data.name,
                pluginId: message.data.pluginId,
                connectedAt: new Date(),
                lastActivity: new Date()
            });

            updatePluginStatus(true);
            updateConnectedPluginsList();

            // 发送配置响应
            const config = {
                apiBase: 'http://localhost:8080/api',
                token: 'mock_token_' + Math.random().toString(36).substr(2, 9),
                userInfo: {
                    id: 1,
                    name: '测试用户',
                    email: '<EMAIL>'
                },
                projectInfo: {
                    id: 'proj_001',
                    name: '测试项目',
                    version: '1.0.0'
                }
            };

            const configMessage = {
                type: 'config',
                to: 'plugin',
                timestamp: Date.now(),
                data: config
            };

            currentChannel.postMessage(configMessage);
            logMessage('主应用', JSON.stringify(configMessage, null, 2), 'sent');
        }

        // 处理数据消息
        function handleDataMessage(message) {
            console.log('收到插件数据:', message.dataType, message.data);
            // 这里可以处理插件发送的各种数据
        }

        // 处理请求消息
        function handleRequestMessage(message) {
            const { requestId, dataType, params } = message;
            
            // 模拟数据响应
            let responseData = {};
            let success = true;
            let error = null;

            switch (dataType) {
                case 'userInfo':
                    responseData = {
                        id: 1,
                        name: '测试用户',
                        email: '<EMAIL>',
                        role: 'admin'
                    };
                    break;
                case 'projectInfo':
                    responseData = {
                        id: 'proj_001',
                        name: '测试项目',
                        version: '1.0.0',
                        description: '这是一个测试项目'
                    };
                    break;
                case 'openFile':
                    responseData = {
                        fileName: 'test-model.json',
                        content: {
                            nodes: [
                                { id: 'node1', type: 'processor', x: 100, y: 100 },
                                { id: 'node2', type: 'queue', x: 300, y: 100 }
                            ],
                            edges: [
                                { id: 'edge1', source: 'node1', target: 'node2' }
                            ]
                        }
                    };
                    break;
                case 'saveFile':
                    responseData = {
                        success: true,
                        filePath: '/saved/files/' + (params.fileName || 'untitled.json'),
                        savedAt: new Date().toISOString()
                    };
                    break;
                default:
                    success = false;
                    error = `未知的请求类型: ${dataType}`;
            }

            // 发送响应
            const responseMessage = {
                type: `response_${requestId}`,
                success: success,
                data: responseData,
                error: error,
                timestamp: Date.now()
            };

            currentChannel.postMessage(responseMessage);
            logMessage('主应用', `响应请求 ${dataType}: ${JSON.stringify(responseMessage, null, 2)}`, 'sent');
        }

        // 发送消息到插件
        function sendMessageToPlugin() {
            if (!currentChannel) {
                alert('请先打开插件');
                return;
            }

            const messageType = document.getElementById('messageType').value;
            const messageDataStr = document.getElementById('messageData').value;
            
            let messageData = {};
            if (messageDataStr) {
                try {
                    messageData = JSON.parse(messageDataStr);
                } catch (e) {
                    messageData = { message: messageDataStr };
                }
            }

            const message = {
                type: messageType,
                to: 'plugin',
                timestamp: Date.now(),
                data: messageData
            };

            currentChannel.postMessage(message);
            logMessage('主应用', JSON.stringify(message, null, 2), 'sent');
        }

        // 更新插件状态
        function updatePluginStatus(connected) {
            const statusDiv = document.getElementById('pluginStatus');
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = '插件已连接';
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = '插件未连接';
            }
        }

        // 更新已连接插件列表
        function updateConnectedPluginsList() {
            const container = document.getElementById('connectedPlugins');
            
            if (connectedPlugins.size === 0) {
                container.innerHTML = '<p>暂无插件连接</p>';
                return;
            }

            let html = '';
            connectedPlugins.forEach((plugin, pluginId) => {
                html += `
                    <div class="plugin-item">
                        <strong>${plugin.name}</strong> (${pluginId})<br>
                        连接时间: ${plugin.connectedAt.toLocaleString()}<br>
                        最后活动: ${plugin.lastActivity.toLocaleString()}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 记录消息日志
        function logMessage(sender, content, type) {
            const logDiv = document.getElementById('messageLog');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-item message-${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <strong>[${timestamp}] ${sender}:</strong><br>
                <pre>${content}</pre>
            `;
            
            logDiv.appendChild(messageDiv);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
        }

        // 页面加载时生成插件ID
        window.onload = function() {
            generatePluginId();
            logMessage('系统', '模拟主应用已启动', 'sent');
        };
    </script>
</body>
</html>
